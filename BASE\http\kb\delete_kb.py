from BASE.services.knowledge_bases import get_knowledge_base_by_id, delete_knowledge_base
from BASE.vdb.qdrant import get_qdrant_client
from logger.log import logger


@logger.catch()
async def delete_kb_source(kbid: str, source: str = "both"):
    """
    Delete a knowledge base by ID using functional programming approach.

    Args:
        kbid: Knowledge base ID to delete
        source: Source to delete from (currently only supports local)

    Returns:
        Dictionary with deletion status and message
    """
    if not kbid:
        logger.error("No knowledge base ID provided")
        return {"status": "error", "message": "Knowledge base ID is required"}

    logger.info(f"Processing delete request for knowledge base: {kbid}")

    try:
        # Check if knowledge base exists
        kb_info = get_knowledge_base_by_id(kbid)
        if not kb_info:
            logger.warning(f"Knowledge base not found: {kbid}")
            return {"status": "error", "message": f"Knowledge base not found: {kbid}"}

        logger.info(f"Found knowledge base: {kb_info.get('name', 'Unknown')}")

        # Delete from Qdrant collection
        try:
            qdrant_client = get_qdrant_client()
            collection_name = kbid

            # Check if collection exists and delete it
            collections = await qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if collection_name in collection_names:
                await qdrant_client.delete_collection(collection_name)
                logger.info(f"Qdrant collection '{collection_name}' deleted successfully")
            else:
                logger.warning(f"Qdrant collection '{collection_name}' not found")

        except Exception as e:
            logger.error(f"Error deleting Qdrant collection: {e}")
            # Continue with database deletion even if Qdrant deletion fails

        # Delete from database
        delete_result = delete_knowledge_base(kbid)

        if delete_result.deleted_count > 0:
            logger.info(f"Knowledge base deleted successfully: {kbid}")
            return {
                "status": "success",
                "message": "Knowledge base successfully deleted",
                "kbid": kbid
            }
        else:
            logger.error(f"Failed to delete knowledge base from database: {kbid}")
            return {"status": "error", "message": "Failed to delete knowledge base from database"}

    except Exception as e:
        logger.error(f"Error deleting knowledge base {kbid}: {e}")
        return {"status": "error", "message": f"Error deleting knowledge base: {str(e)}"}