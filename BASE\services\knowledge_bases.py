from pathlib import Path
from database.MicroMongo import MongoClient

# Initialize MicroMongo client and database
_client = None
_db = None

def get_knowledge_bases_collection():
    """Get the knowledge_bases collection from MicroMongo database."""
    global _client, _db

    if _client is None:
        _client = MongoClient('knowledge_bases_db')
        _db = _client['codemate']

    return _db['knowledge_bases']

def create_knowledge_base(name: str, kbid: str, metadata: dict = None, is_auto_indexed: bool = False):
    """Create a new knowledge base entry."""
    collection = get_knowledge_bases_collection()

    kb_data = {
        "name": name,
        "id": kbid,
        "metadata": metadata or {},
        "isAutoIndexed": is_auto_indexed
    }

    result = collection.insert_one(kb_data)
    return result.inserted_id

def get_knowledge_base_by_name(name: str):
    """Get a knowledge base by name."""
    collection = get_knowledge_bases_collection()
    return collection.find_one({"name": name})

def get_knowledge_base_by_id(kbid: str):
    """Get a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.find_one({"id": kbid})

def get_knowledge_base_by_path(path: str):
    """Get a knowledge base by path."""
    collection = get_knowledge_bases_collection()
    normalized_path = str(Path(path).resolve())
    return collection.find_one({"metadata.path": normalized_path})

def get_auto_indexed_knowledge_base_by_path(path: str):
    """Get an auto-indexed knowledge base by path."""
    collection = get_knowledge_bases_collection()
    normalized_path = str(Path(path).resolve())
    return collection.find_one({
        "metadata.path": normalized_path,
        "isAutoIndexed": True
    })

def exists_knowledge_base_by_name(name: str) -> bool:
    """Check if a knowledge base with the given name exists."""
    return get_knowledge_base_by_name(name) is not None

def exists_knowledge_base_by_id(kbid: str) -> bool:
    """Check if a knowledge base with the given ID exists."""
    return get_knowledge_base_by_id(kbid) is not None

def exists_knowledge_base_by_path(path: str) -> bool:
    """Check if a knowledge base with the given path exists."""
    return get_knowledge_base_by_path(path) is not None

def exists_auto_indexed_knowledge_base_by_path(path: str) -> bool:
    """Check if an auto-indexed knowledge base with the given path exists."""
    return get_auto_indexed_knowledge_base_by_path(path) is not None

def update_knowledge_base(kbid: str, update_data: dict):
    """Update a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": update_data})

def delete_knowledge_base(kbid: str):
    """Delete a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.delete_one({"id": kbid})

def list_all_knowledge_bases():
    """Get all knowledge bases."""
    collection = get_knowledge_bases_collection()
    return list(collection.find())

def count_knowledge_bases():
    """Count total number of knowledge bases."""
    collection = get_knowledge_bases_collection()
    return collection.count_documents({})

def from_chunks(
        metadata: QdrantKnowledgeBaseMetadata,
        chunks: list[QdrantKnowledgeBaseChunk],
    ) -> "QdrantKnowledgeBase":
        # if not processed_data:
        #     raise ValueError("No processed data provided")

        """Save processed chunks to local Qdrant database."""
        vector_size = len(chunks[0].embeddings)
        # print(vector_size)

        client = get_db_client()
        client.create_collection(
            collection_name=metadata.id,
            vectors_config={
                "vectors": rest.VectorParams(
                    size=vector_size, distance=rest.Distance.DOT
                )
            },
        )
        for chunk in chunks:
            client.upsert(
                collection_name=metadata.id,
                points=[
                    PointStruct(
                        id=chunk.metadata.id,
                        vector={"vectors": chunk.embeddings},
                        payload={"metadata": chunk.metadata},
                    ),
                ],
            )

        # Convert metadata to a dictionary
        metadata_dict = metadata.model_dump()

        # Create the QdrantKnowledgeBase instance
        kb = QdrantKnowledgeBase(**metadata_dict)
        kb.status = QdrantKnowledgebaseStatus.READY

        # Insert the knowledge base into the database
        QdrantKnowledgeBase._get_kbdb().insert_one(kb.model_dump())

        return kb