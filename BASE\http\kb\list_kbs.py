import time
import httpx
from typing import List, Dict, Any
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request

from client_server.core.constants import SSL_CONTEXT
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_operation_stats, log_api_call_stats
from client_server.services.db_handler import Database
from client_server.utils.models.knowledgebase import (
    QdrantKnowledgeBase,
    QdrantKnowledgebaseScope,
    QdrantKnowledgebaseSource,
    QdrantKnowledgebaseStatus,
    QdrantKnowledgebaseType,
    QdrantKnowledgebaseSyncConfig_t,
)
from . import route

db = Database()
knowledge_base = db["knowledge_bases"]


def determine_sync_status(kb_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Determine cloud synchronization status for a knowledge base.

    Simplified logic:
    - Local KBs without cloud_id → show upload option
    - Local KBs with cloud_id → show sync option
    - Remote KBs → always synced

    Args:
        kb_dict: Knowledge base dictionary

    Returns:
        Dictionary with sync status information
    """
    cloud_id = kb_dict.get('cloud_id')
    source = kb_dict.get('source', '').upper()

    # Initialize sync status info
    sync_info = {
        'sync_status': 'unknown',
        'cloud_sync_available': False,
        'can_upload': False,
        'can_sync': False,
        'status_reason': ''
    }

    # Remote/Cloud KBs are always considered synced
    if source == 'REMOTE':
        sync_info.update({
            'sync_status': 'synced',
            'cloud_sync_available': True,
            'can_upload': False,
            'can_sync': False,
            'status_reason': 'Cloud knowledge base - always synced'
        })
        return sync_info

    # Local KBs - simplified logic based on cloud_id presence
    if not cloud_id:
        # No cloud_id means never uploaded to cloud
        sync_info.update({
            'sync_status': 'upload_needed',
            'cloud_sync_available': True,
            'can_upload': True,
            'can_sync': False,
            'status_reason': 'Local KB not yet uploaded to cloud'
        })
    else:
        # Has cloud_id - show sync option
        sync_info.update({
            'sync_status': 'sync_available',
            'cloud_sync_available': True,
            'can_upload': False,
            'can_sync': True,
            'status_reason': 'KB can be synced with cloud'
        })

    return sync_info


def enhance_kb_with_sync_status(kb_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhance a knowledge base dictionary with cloud sync status information.

    Args:
        kb_dict: Original knowledge base dictionary

    Returns:
        Enhanced knowledge base dictionary with sync status fields
    """
    enhanced_kb = kb_dict.copy()
    sync_info = determine_sync_status(kb_dict)

    # Add sync status fields to the KB response
    enhanced_kb.update({
        'sync_status': sync_info['sync_status'],
        'cloud_sync_available': sync_info['cloud_sync_available'],
        'can_upload': sync_info['can_upload'],
        'can_sync': sync_info['can_sync'],
        'status_reason': sync_info['status_reason']
    })

    return enhanced_kb


def combine_local_and_cloud_kbs(local_kbs: List[Dict[str, Any]], cloud_kbs: List[QdrantKnowledgeBase]) -> List[Dict[str, Any]]:
    """
    Combine local and cloud knowledge bases with simplified logic.

    No duplicate filtering - each KB is unique by path (enforced at creation time).
    Simply combine local and cloud KBs and enhance with sync status.

    Args:
        local_kbs: List of local knowledge base dictionaries
        cloud_kbs: List of cloud knowledge base objects

    Returns:
        Combined list of enhanced knowledge bases
    """
    LOGGER.info(f"Combining {len(local_kbs)} local KBs and {len(cloud_kbs)} cloud KBs")

    combined_kbs = []

    # Add all local KBs with sync status enhancement
    for local_kb in local_kbs:
        enhanced_kb = enhance_kb_with_sync_status(local_kb)
        combined_kbs.append(enhanced_kb)
        LOGGER.debug(f"Added local KB: '{local_kb.get('name', 'Unknown')}' with sync status: {enhanced_kb.get('sync_status')}")

    # Add all cloud KBs with sync status enhancement
    for cloud_kb in cloud_kbs:
        cloud_kb_dict = cloud_kb.model_dump()
        enhanced_kb = enhance_kb_with_sync_status(cloud_kb_dict)
        combined_kbs.append(enhanced_kb)
        LOGGER.debug(f"Added cloud KB: '{cloud_kb_dict.get('name', 'Unknown')}' with sync status: {enhanced_kb.get('sync_status')}")

    LOGGER.info(f"Combined total: {len(combined_kbs)} knowledge bases")
    return combined_kbs


async def get_kb_list_from_cloud(session: str | None) -> list[QdrantKnowledgeBase]:
    """Fetch knowledge base list from cloud with comprehensive logging"""
    start_time = time.time()

    LOGGER.info(f"Starting cloud KB list fetch - Session: {session[:8] if session else 'None'}...")

    if not session:
        LOGGER.warning("No session found, skipping KB list from cloud")
        return []

    try:
        # Get KB list from cloud
        cloud_request_start = time.time()
        LOGGER.debug("Making request to cloud KB collections endpoint")

        async with httpx.AsyncClient(verify=SSL_CONTEXT) as client:
            response = await client.get(
                f"https://codebase.v3.codemate.ai/cloud_sync/collections/test",
                headers={"x-session": session},
            )

        cloud_request_time = time.time() - cloud_request_start
        LOGGER.info(f"Cloud KB request completed - Status: {response.status_code}, Time: {cloud_request_time:.3f}s")

        log_api_call_stats(
            "https://codebase.v3.codemate.ai/cloud_sync/collections/test",
            "GET",
            response.status_code,
            cloud_request_time,
            len(response.content) if response.content else 0
        )

        if response.status_code != 200:
            LOGGER.error(f"Cloud KB request failed with status {response.status_code}: {response.text}")
            return []

        response_data = response.json()

        LOGGER.debug(f" new Cloud KB response: {response_data}")

        # Handle new API response structure with separate personal and shared arrays
        if "knowledge_bases" in response_data:
            # Legacy format - single array
            knowledge_bases = response_data["knowledge_bases"]
            LOGGER.debug("Using legacy cloud API response format (single knowledge_bases array)")
        else:
            # New format - separate personal and shared arrays
            personal_kbs = response_data.get("personal", [])
            shared_kbs = response_data.get("shared", [])

            # Combine both arrays into a single list
            # knowledge_bases = personal_kbs + shared_kbs
            knowledge_bases = shared_kbs
            LOGGER.debug(f"Using new cloud API response format (personal: {len(personal_kbs)}, shared: {len(shared_kbs)})")

        LOGGER.info(f"Retrieved {len(knowledge_bases)} knowledge bases from cloud")

        # Process cloud KBs
        processing_start = time.time()
        kbs = []
        for i, kb in enumerate(knowledge_bases):
            """
            Expected cloud KB format:
            {
                "uuid": "8e021c7e-60f4-4463-9b4e-fd68474f1267",
                "name": "client_server copy",
                "last_updated_at": 1748434829,
                "type": "codebase",
                "shared": False,
                "team": False,
            }
            """
            try:
                LOGGER.debug(f"Processing cloud KB {i+1}/{len(knowledge_bases)}: {kb.get('name', 'Unknown')}")

                kb_name = kb["name"]
                kb_type = kb.get("type", "codebase")
                is_shared = kb.get("shared", False)
                is_team = kb.get("team", False)

                # Determine display name with scope indicator
                display_name = kb_name + (
                    " (Shared)" if is_shared
                    else " (Team)" if is_team
                    else " (Personal)"
                )

                processed_kb = QdrantKnowledgeBase(
                    id=kb["uuid"],
                    cloud_id=kb["uuid"],
                    description=kb_name,
                    isAutoIndexed=False,
                    name=display_name,
                    progress=None,
                    scope=(
                        QdrantKnowledgebaseScope.Organization
                        if is_team or is_shared
                        else QdrantKnowledgebaseScope.Personal
                    ),
                    source=QdrantKnowledgebaseSource.Remote,
                    status=QdrantKnowledgebaseStatus.READY,
                    type=QdrantKnowledgebaseType(kb_type),
                    syncConfig=QdrantKnowledgebaseSyncConfig_t(
                        enabled=True,
                        lastSynced=(
                            int(kb["last_updated_at"] * 1000)
                            if "last_updated_at" in kb
                            else 1
                        ),
                    ),
                )

                kbs.append(processed_kb)
                LOGGER.debug(f"Successfully processed cloud KB: {display_name} (Type: {kb_type})")

            except Exception as e:
                LOGGER.warning(f"Error processing cloud KB record {i+1}: {e}")
                LOGGER.debug(f"Problematic KB data: {kb}")
                continue

        processing_time = time.time() - processing_start
        total_time = time.time() - start_time

        LOGGER.info(
            f"Cloud KB processing completed - "
            f"Processed: {len(kbs)}/{len(knowledge_bases)}, "
            f"Processing time: {processing_time:.3f}s, "
            f"Total time: {total_time:.3f}s"
        )

        return kbs

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error fetching cloud KB list after {total_time:.3f}s: {e}")
        return []



async def list_all_knowledge_bases(include_cloud: bool, request: Request):
    """List all knowledge bases with comprehensive logging"""
    start_time = time.time()
    session_id = request.headers.get("x-session", "unknown")

    LOGGER.info(
        f"Starting KB list request - "
        f"Include cloud: {include_cloud}, "
        f"Session: {session_id[:8] if session_id and session_id != 'unknown' else 'None'} "
        f"(raw: '{session_id}')..."
    )

    try:
        # Query local database
        db_query_start = time.time()
        LOGGER.debug("Querying local knowledge base database")
        kb_records = list(knowledge_base.find())
        db_query_time = time.time() - db_query_start

        LOGGER.info(f"Local DB query completed - Found: {len(kb_records)} records, Time: {db_query_time:.3f}s")

        # Process local records
        processing_start = time.time()
        result = []
        for i, record in enumerate(kb_records):
            try:
                LOGGER.debug(f"Processing local KB {i+1}/{len(kb_records)}: {record.get('name', 'Unknown')}")
                kb = QdrantKnowledgeBase(**record)
                result.append(kb.model_dump())
            except Exception as e:
                LOGGER.warning(f"Error processing local KB record {i+1}: {e}")
                LOGGER.debug(f"Problematic record: {record}")
                continue

        processing_time = time.time() - processing_start
        LOGGER.info(
            f"Local KB processing completed - "
            f"Processed: {len(result)}/{len(kb_records)}, "
            f"Time: {processing_time:.3f}s"
        )

        # Get KB list from cloud
        cloud_kbs = []
        if include_cloud:
            try:
                cloud_fetch_start = time.time()
                LOGGER.debug("Fetching knowledge bases from cloud")
                # Use session from request headers instead of global state
                # Only use session if it's present and not the default "unknown" value
                session = session_id if session_id and session_id != "unknown" else None
                cloud_kbs = await get_kb_list_from_cloud(session)
                cloud_fetch_time = time.time() - cloud_fetch_start

                LOGGER.info(f"Cloud KB fetch completed - Found: {len(cloud_kbs)} KBs, Time: {cloud_fetch_time:.3f}s")
            except Exception as e:
                LOGGER.warning(f"Error getting KB list from cloud: {e}")
                # Continue with local KBs even if cloud fails
        else:
            LOGGER.debug("Skipping cloud KB fetch (include_cloud=False)")

        # Combine local and cloud KBs with simplified sync status logic
        combined_kbs = combine_local_and_cloud_kbs(result, cloud_kbs)
        total_time = time.time() - start_time

        log_operation_stats("list_kbs", start_time, len(combined_kbs), success=True)

        LOGGER.info(
            f"KB list request completed - "
            f"Local: {len(result)}, Cloud: {len(cloud_kbs)}, "
            f"Combined Total: {len(combined_kbs)}, Time: {total_time:.3f}s"
        )

        return combined_kbs

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error listing knowledge bases after {total_time:.3f}s: {e}")
        log_operation_stats("list_kbs", start_time, 0, success=False)
        raise HTTPException(status_code=500, detail=str(e))
