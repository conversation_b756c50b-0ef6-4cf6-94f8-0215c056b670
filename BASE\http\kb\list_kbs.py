
from BASE.services.knowledge_bases import list_all_knowledge_bases
from logger.log import logger


@logger.catch()
def list_knowledge_bases():
    """
    List all knowledge bases using functional programming approach.

    Returns:
        Dictionary with list of knowledge bases and status
    """
    logger.info("Processing knowledge base list request")

    try:
        # Get all knowledge bases from the database
        kb_list = list_all_knowledge_bases()

        if not kb_list:
            logger.info("No knowledge bases found")
            return {
                "status": "success",
                "message": "No knowledge bases found",
                "knowledge_bases": [],
                "count": 0
            }

        logger.info(f"Found {len(kb_list)} knowledge bases")

        # Return the list with basic metadata
        return {
            "status": "success",
            "message": f"Retrieved {len(kb_list)} knowledge bases",
            "knowledge_bases": kb_list,
            "count": len(kb_list)
        }

    except Exception as e:
        logger.error(f"Error listing knowledge bases: {e}")
        return {
            "status": "error",
            "message": f"Error listing knowledge bases: {str(e)}",
            "knowledge_bases": [],
            "count": 0
        }
