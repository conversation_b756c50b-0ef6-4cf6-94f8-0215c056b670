from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id


async def delete_kb_from_cloud(kbid: str):
    """Delete a knowledge base from cloud"""
    try:
        session = G_SESSION_ID.get()
        if not session:
            LOGGER.warning("No session found, skipping KB deletion from cloud")
            return
        async with httpx.AsyncClient(verify=SSL_CONTEXT) as client:
            response = await client.post(
                f"https://codebase.v3.codemate.ai/delete/kb",
                json={"kbid": kbid},
                headers={"x-session": session},
            )
            return response.json()
    except Exception as e:
        LOGGER.error(f"Error deleting knowledge base from cloud: {e}")
        raise


@logger.catch()
async def delete_kb_source(kbid: str, source: str = "both"):
    """Delete a knowledge base by ID"""
    start_time = time.time()
    LOGGER.info(f"Processing delete request for knowledge base: {data.kbid}")

    # Try deleting from cloud regardless of local db
    try:
        await delete_kb_from_cloud(data.kbid)
    except Exception as e:
        LOGGER.error(f"Error deleting knowledge base from cloud: {e}")

    try:
        # Get KB info before deletion for logging
        kb_info = knowledge_base.find_one({"id": data.kbid})
        if not kb_info:
            LOGGER.warning(f"Knowledge base not found: {data.kbid}")
            raise HTTPException(
                status_code=404, detail=f"Knowledge base not found: {data.kbid}"
            )

        # Delete from database
        db_start = time.time()
        delete_result = knowledge_base.delete_one({"id": data.kbid})
        db_time = time.time() - db_start
        LOGGER.debug(f"Database deletion completed in {db_time:.3f}s")

        # Delete files
        fs_start = time.time()
        collection_path = (
            PathSelector.get_qdrant_db_path() / f".vdb/collection/{data.kbid}"
        )

        if collection_path.exists():
            collection_size = sum(
                f.stat().st_size for f in collection_path.rglob("*") if f.is_file()
            )
            LOGGER.debug(
                f"Collection size before deletion: {collection_size/1024/1024:.2f}MB"
            )

            shutil.rmtree(collection_path, ignore_errors=True)
            fs_time = time.time() - fs_start
            LOGGER.debug(f"Filesystem deletion completed in {fs_time:.3f}s")
        else:
            LOGGER.debug(f"Collection path not found: {collection_path}")

        total_time = time.time() - start_time
        LOGGER.info(
            f"Knowledge base deletion completed - ID: {data.kbid}, "
            f"DB time: {db_time:.3f}s, Total time: {total_time:.3f}s"
        )

        # Log operation statistics
        stats = log_operation_stats(
            "kb_deletion", start_time, 1, success=True  # One KB deleted
        )

        return {"message": "Knowledge base successfully deleted", "kbid": data.kbid}

    except HTTPException:
        raise

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error deleting knowledge base after {total_time:.3f}s: {e}")
        raise HTTPException(status_code=500, detail=str(e))